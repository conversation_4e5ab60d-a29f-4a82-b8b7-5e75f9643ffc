<template>
  <view class="scanOutBound-page">
    <nav-bar back>扫码领料</nav-bar>

    <view class="content">
      <!-- 产品信息显示区域 -->
      <view class="info-section">
        <view class="section-title">产品信息</view>

        <view class="info-item">
          <text class="info-label">工单号：</text>
          <text class="info-value">{{ orderCode }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">产品型号：</text>
          <text class="info-value">{{ productName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">数量：</text>
          <text class="info-value">{{ quantity }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">板型：</text>
          <text class="info-value">{{ boardType }}</text>
        </view>

        <text class="info-label">已领取数量:</text>
        <text class="info-value">
           上板：0   下板：0    单板：0
        </text>
      </view>
    </view>

    <!-- 底部固定操作按钮 -->
    <view class="bottom-action">
      <button class="scan-btn" @click="showBoardTypeDialog">
        选择板型并扫码
      </button>
    </view>
  </view>
</template>

<script>
import { outboundThree, showMessage, getStepTaskDetail } from '@/api/workOrders.js'

export default {
  name: "scanCode",
  data() {
    return {
      // 页面参数
      orderCode: '',
      stepTaskId: '',
      quantity: '',
      boardType: '',
      productName: ''
    }
  },

  onLoad(options) {
    // 接收页面参数
    this.orderCode = decodeURIComponent(options.orderCode || '')
    this.stepTaskId = options.stepTaskId || ''
    this.quantity = options.quantity || ''
    this.boardType =decodeURIComponent(options.boardType || '')
    this.productName = decodeURIComponent(options.productName || '')

    console.log('scanCode页面接收参数:', {
      orderCode: this.orderCode,
      stepTaskId: this.stepTaskId,
      quantity: this.quantity,
      boardType: this.boardType,
      productName: this.productName
    })

    // 参数验证
    if (!this.orderCode || !this.stepTaskId || !this.quantity || !this.boardType || !this.productName) {
      showMessage('页面参数不完整', 'error')
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
      return
    }

    // 页面加载完成后，延迟一下直接显示板型选择对话框
    this.$nextTick(() => {
      setTimeout(() => {
        this.showBoardTypeDialog()
      }, 500) // 延迟500ms确保页面完全加载
    })
  },

  methods: {
    /** 显示板型选择对话框 */
    showBoardTypeDialog() {
      console.log('显示板型选择对话框，产品板型:', this.boardType)

      // 根据产品板型构建选项列表
      let itemList = []
      let boardTypeMapping = []

      if (this.boardType === '上下板') {
        // 上下板产品：显示上板入库和下板入库选项
        itemList.push('上板领料')
        boardTypeMapping.push('上板')

        itemList.push('下板领料')
        boardTypeMapping.push('下板')
      } else if (this.boardType === '单板') {
        // 单板产品：只显示单板入库选项
        itemList.push('单板领料')
        boardTypeMapping.push('单板')
      } else {
        // 其他情况：显示所有选项
        itemList.push('上板领料')
        boardTypeMapping.push('上板')

        itemList.push('下板领料')
        boardTypeMapping.push('下板')

        itemList.push('单板领料')
        boardTypeMapping.push('单板')
      }

      console.log('显示板型选择对话框:', {
        boardType: this.boardType,
        itemList: itemList,
        boardTypeMapping: boardTypeMapping
      })

      // 显示板型选择对话框
      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const selectedBoardType = boardTypeMapping[res.tapIndex]
          console.log('用户选择的板型:', selectedBoardType)

          // 选择完成后进入扫码流程
          this.startScanCode(selectedBoardType)
        },
        fail: (err) => {
          console.log('用户取消选择板型')
        }
      })
    },

    /** 开始扫码流程 */
    async startScanCode(boardType) {
      console.log('开始扫码，板型:', boardType)

      try {
        // 在扫码前先检查库存状态
        await this.checkInventoryStatus(boardType)

        // 库存检查通过，开始扫码
        this.performScanCode(boardType)

      } catch (error) {
        console.error('库存检查失败:', error)
        // 即使库存检查失败，也允许用户继续扫码
        this.performScanCode(boardType)
      }
    },

    /** 检查库存状态 */
    async checkInventoryStatus(boardType) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '检查领取状态...'
        })

        console.log('开始获取工序任务详细信息，stepTaskId:', this.stepTaskId)
        const response = await getStepTaskDetail(this.stepTaskId)
        console.log('工序任务详细信息API响应:', response)

        uni.hideLoading()

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          const stepTaskDetail = response.data
          const onBoardCount = stepTaskDetail?.onBoard || 0
          const downBoardCount = stepTaskDetail?.downBoard || 0
          const oneBoardCount = stepTaskDetail?.oneBoard || 0
          const targetQuantity = parseInt(this.quantity) || 0

          console.log('获取到的已领取数量:', {
            onBoard: onBoardCount,
            downBoard: downBoardCount,
            oneBoard: oneBoardCount,
            targetQuantity: targetQuantity,
            selectedBoardType: boardType
          })

          // 检查当前选择的板型是否已完成领取
          let currentCount = 0
          let boardTypeName = ''

          if (boardType === '上板') {
            currentCount = onBoardCount
            boardTypeName = '上板'
          } else if (boardType === '下板') {
            currentCount = downBoardCount
            boardTypeName = '下板'
          } else if (boardType === '单板') {
            currentCount = oneBoardCount
            boardTypeName = '单板'
          }

          // 如果已领取数量达到或超过目标数量，显示提示
          if (currentCount >= targetQuantity) {
            return new Promise((resolve, reject) => {
              uni.showModal({
                title: '领取状态提示',
                content: `${boardTypeName}已领料完成！\n\n目标数量：${targetQuantity}\n已领取数量：${currentCount}\n\n是否继续扫码？`,
                confirmText: '继续扫码',
                cancelText: '取消',
                success: (res) => {
                  if (res.confirm) {
                    resolve() // 用户选择继续
                  } else {
                    reject(new Error('用户取消操作')) // 用户取消
                  }
                }
              })
            })
          } else if (currentCount > 0) {
            // 如果有部分领取记录，显示信息提示
            uni.showToast({
              title: `${boardTypeName}已领取${currentCount}/${targetQuantity}`,
              icon: 'none',
              duration: 2000
            })
          }
        } else {
          console.warn('获取工序任务详细信息失败或响应异常:', response)
          // 即使获取失败，也继续扫码流程，但给出提示
          uni.showToast({
            title: '无法获取领取状态，继续扫码',
            icon: 'none',
            duration: 2000
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('检查库存状态失败:', error)

        // 如果是用户取消操作，抛出错误
        if (error.message === '用户取消操作') {
          throw error
        }

        // 其他错误情况，给出提示但不阻止扫码
        uni.showToast({
          title: '检查领取状态失败，继续扫码',
          icon: 'none',
          duration: 2000
        })
      }
    },

    /** 执行扫码操作 */
    performScanCode(boardType) {
      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          console.log('扫码成功，结果:', res.result)
          // 扫码成功，处理扫码结果
          this.processScanResult(res.result, boardType)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          // 扫码失败处理
          this.handleScanError(err)
        }
      })
    },

    /** 处理扫码结果 */
    processScanResult(scanData, boardType) {
      try {
        console.log('处理扫码结果:', {
          scanData: scanData,
          boardType: boardType,
          productName: this.productName,
          quantity: this.quantity
        })

        // 解析扫码数据，提取 purchase_no 和 batch_no
        const scanResult = this.parseQRCode(scanData)
        console.log('解析出的扫码数据:', scanResult)

        // 验证必需字段
        if (!scanResult.purchaseNo || !scanResult.batchNo) {
          uni.showToast({
            title: `扫码数据格式错误，无法解析采购单号或批次号。\n扫码内容：${scanData}`,
            icon: 'none',
            duration: 5000
          })
          return
        }

        // 构建确认内容
        const content = `确定要领料 ${this.productName} 的${boardType}吗？\n数量：${this.quantity}\n采购单号：${scanResult.purchaseNo}\n批次号：${scanResult.batchNo}`

        // 显示扫码结果确认弹窗
        uni.showModal({
          title: '扫码结果确认',
          content: content,
          confirmText: '确定',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户确认，执行出库操作
              this.performOutbound(scanResult, boardType)
            }
          }
        })
      } catch (error) {
        console.error('处理扫码结果失败:', error)
        uni.showToast({
          title: '处理扫码结果失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        })
      }
    },

    /**
     * 解析二维码内容
     * 二维码格式：label_type:purchase|purchase_no:PO20250731006|batch_no:20250731-003
     * @param {string} scanData 扫码数据
     * @returns {Object} 解析结果 {purchaseNo, batchNo}
     */
    parseQRCode(scanData) {
      try {
        console.log('开始解析二维码数据:', scanData)

        const result = {
          purchaseNo: null,
          batchNo: null
        }

        // 验证输入数据
        if (!scanData || typeof scanData !== 'string') {
          console.warn('二维码数据为空或格式不正确')
          return result
        }

        // 验证二维码格式是否包含必要的标识
        if (!scanData.includes('purchase_no:') || !scanData.includes('batch_no:')) {
          console.warn('二维码格式不正确，缺少必要字段')
          return result
        }

        // 使用正则表达式提取字段值
        const purchaseNoMatch = scanData.match(/purchase_no:([^|]+)/i)
        const batchNoMatch = scanData.match(/batch_no:([^|]+)/i)

        if (purchaseNoMatch && purchaseNoMatch[1]) {
          result.purchaseNo = purchaseNoMatch[1].trim()
          console.log('解析出的purchase_no:', result.purchaseNo)
        }

        if (batchNoMatch && batchNoMatch[1]) {
          result.batchNo = batchNoMatch[1].trim()
          console.log('解析出的batch_no:', result.batchNo)
        }

        // 如果正则匹配失败，尝试手动解析
        if (!result.purchaseNo || !result.batchNo) {
          console.log('正则匹配失败，尝试手动解析')
          const parts = scanData.split('|')

          for (const part of parts) {
            const trimmedPart = part.trim()

            // 解析 purchase_no
            if (!result.purchaseNo && trimmedPart.includes('purchase_no:')) {
              const purchaseNo = trimmedPart.split('purchase_no:')[1]?.trim()
              if (purchaseNo) {
                result.purchaseNo = purchaseNo
                console.log('手动解析出的purchase_no:', result.purchaseNo)
              }
            }

            // 解析 batch_no
            if (!result.batchNo && trimmedPart.includes('batch_no:')) {
              const batchNo = trimmedPart.split('batch_no:')[1]?.trim()
              if (batchNo) {
                result.batchNo = batchNo
                console.log('手动解析出的batch_no:', result.batchNo)
              }
            }
          }
        }

        console.log('最终解析结果:', result)
        return result

      } catch (error) {
        console.error('解析二维码数据失败:', error)
        return {
          purchaseNo: null,
          batchNo: null
        }
      }
    },
    /** 执行出库操作 */
    async performOutbound(scanResult, boardType) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '出库中...'
        })

        // 准备API参数
        const formData = {
          quantity: this.quantity,           // 从页面参数获取的数量
          purchaseNo: scanResult.purchaseNo,           // 从扫码结果解析的采购单号
          itemName: this.productName,       // 从页面参数获取的产品名称
          boardType: boardType,             // 用户选择的板型（"上板"/"下板"/"单板"）
          stepTaskId: this.stepTaskId , // 从页面参数获取的工序任务ID
          batchNo: scanResult.batchNo

        }

        console.log('出库API调用参数:', formData)

        // 调用出库API
        const response = await outboundThree(formData)

        console.log('出库API响应:', response)
        console.log('响应码:', response.code)
        console.log('响应消息:', response.message)

        uni.hideLoading()

        // 根据WMS API规范，成功响应的code应该是0
        if (response && (response.code === 0 || response.code === 200)) {
          // 显示成功提示
          uni.showToast({
            title: '出库成功',
            icon: 'success',
            duration: 2000
          })

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)

        } else {
          console.error('出库失败详情:', {
            code: response?.code,
            message: response?.message,
            data: response?.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response?.message || response?.msg || '出库失败，请稍后重试'
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 5000
          })
        }

      } catch (error) {
        uni.hideLoading()

        console.error('出库操作异常:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        })

        // 处理不同类型的错误
        let errorMessage = '网络请求失败，请检查网络连接'

        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试'
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络'
          } else {
            errorMessage = error.message
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 5000
        })
      }
    },

    /** 处理扫码失败 */
    handleScanError(error) {
      console.error('扫码失败:', error)

      let errorMessage = '扫码失败'

      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMessage = '用户取消扫码'
        } else if (error.errMsg.includes('fail')) {
          errorMessage = '扫码功能异常，请重试'
        }
      }

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
    }
  }
}
</script>

<style scoped lang="scss">
.scanOutBound-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 88rpx; /* 为导航栏预留空间 */
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

.content {
  padding: 20rpx;
  min-height: calc(100vh - 208rpx); /* 减去顶部和底部的空间 */
}

/* 信息显示区域 */
.info-section {
  background: #fff;
  border-radius: 12rpx;
  margin-top: 60rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 操作区域 */
.action-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    }
  }
}

/* 底部固定按钮区域 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.scan-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  }
}
</style>