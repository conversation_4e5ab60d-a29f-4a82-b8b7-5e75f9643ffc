<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码工序步骤逻辑更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        .feature {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #007aff;
        }
        .code-block {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .info {
            color: #007aff;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .update {
            color: #dc3545;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .highlight {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 WMS 二维码生成功能 - 工序步骤逻辑更新</h1>
        
        <div class="section">
            <h2>✅ 更新内容概述</h2>
            <div class="feature">
                <strong class="update">核心更新</strong>
                <p>根据不同的工序步骤调用不同的二维码生成方法和使用不同的 label_type 值</p>
                <ul>
                    <li><strong>申领半成品PCB板及零件</strong> → 使用 <code>printWeldingOutboundQrCode</code> + <code>label_type: "welding_outbound"</code></li>
                    <li><strong>其他工序步骤</strong> → 使用 <code>printSmtOutboundQrCode</code> + <code>label_type: "smt_outbound"</code></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📋 工序步骤映射表</h2>
            <table>
                <thead>
                    <tr>
                        <th>工序步骤名称</th>
                        <th>API 方法</th>
                        <th>label_type</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>领取PCB裸板</td>
                        <td>printSmtOutboundQrCode</td>
                        <td>smt_outbound</td>
                        <td>✅ 保持不变</td>
                    </tr>
                    <tr style="background-color: #fff3cd;">
                        <td><strong>申领半成品PCB板及零件</strong></td>
                        <td><strong>printWeldingOutboundQrCode</strong></td>
                        <td><strong>welding_outbound</strong></td>
                        <td><strong>🔄 已更新</strong></td>
                    </tr>
                    <tr>
                        <td>测试员领取焊接半成品</td>
                        <td>printSmtOutboundQrCode</td>
                        <td>smt_outbound</td>
                        <td>✅ 保持不变</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔧 技术实现细节</h2>
            
            <div class="feature">
                <strong class="info">1. myOrderTask.vue 修改</strong>
                <p>在页面跳转时添加工序步骤名称参数：</p>
                <div class="code-block">
// 跳转到createQRCode页面，传递二维码生成相关参数
const params = {
  styleId: product.styleId,
  styleName: encodeURIComponent(product.styleName || ''),
  orderCode: order.orderCode,
  productName: encodeURIComponent(product.productName || ''),
  boardType: encodeURIComponent(product.boardType || ''),
  stepName: encodeURIComponent(step.stepName || '') // 🔄 新增工序步骤参数
}

uni.navigateTo({
  url: `/pages/warehouse/mytask/createQRCode?${queryString}`
})
                </div>
            </div>

            <div class="feature">
                <strong class="info">2. createQRCode.vue 修改</strong>
                <p>添加工序步骤参数接收和条件判断逻辑：</p>
                
                <h4>📥 参数接收</h4>
                <div class="code-block">
data() {
  return {
    // 页面参数
    styleId: '',
    styleName: '',
    orderCode: '',
    productName: '',
    boardType: '',
    stepName: '', // 🔄 新增工序步骤名称参数
    // ...
  }
},

onLoad(options) {
  // 接收页面参数
  this.styleId = options.styleId || ''
  this.styleName = decodeURIComponent(options.styleName || '')
  this.orderCode = options.orderCode || ''
  this.productName = decodeURIComponent(options.productName || '')
  this.boardType = decodeURIComponent(options.boardType || '')
  this.stepName = decodeURIComponent(options.stepName || '') // 🔄 接收工序步骤参数
  
  this.initBoardOptions()
}
                </div>

                <h4>🔀 条件判断逻辑</h4>
                <div class="code-block">
async generateQRCode() {
  // 根据工序步骤选择不同的 label_type 和 API 方法
  let labelType = 'smt_outbound'
  let apiMethod = printSmtOutboundQrCode

  // 🔄 判断工序步骤，选择对应的方法和 label_type
  if (this.stepName === '申领半成品PCB板及零件') {
    labelType = 'welding_outbound'
    apiMethod = printWeldingOutboundQrCode
  }

  // 构建接口参数
  const params = {
    label_type: labelType, // 🔄 动态设置 label_type
    purchase_no: this.orderCode,
    orderCode: this.orderCode,
    board_type: this.selectedBoardType,
    item_name: this.productName,
    outbound_time: this.getCurrentTime(),
    styleId: parseInt(this.styleId),
    style_name: this.styleName
  }

  // 🔄 调用对应的二维码生成接口
  const response = await apiMethod(params)
  
  // 处理响应...
}
                </div>
            </div>

            <div class="feature">
                <strong class="info">3. 用户界面更新</strong>
                <p>在工单信息中显示工序步骤，让用户清楚知道当前操作的工序：</p>
                <div class="code-block">
&lt;view class="info-item"&gt;
  &lt;text class="info-label"&gt;工序步骤：&lt;/text&gt;
  &lt;text class="info-value"&gt;{{ stepName }}&lt;/text&gt;
&lt;/view&gt;
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧪 测试场景</h2>
            
            <div class="highlight">
                <strong>重点测试场景</strong>
                <p>确保 "申领半成品PCB板及零件" 工序使用正确的 API 方法</p>
            </div>

            <div class="feature">
                <strong class="info">测试步骤</strong>
                <ol>
                    <li><strong>测试 "申领半成品PCB板及零件" 工序</strong>
                        <ul>
                            <li>点击该工序的"生成二维码"按钮</li>
                            <li>验证跳转到 createQRCode.vue 页面</li>
                            <li>检查工序步骤显示是否正确</li>
                            <li>选择板型后点击"生成二维码"</li>
                            <li>验证调用的是 <code>printWeldingOutboundQrCode</code> 方法</li>
                            <li>验证 <code>label_type</code> 为 "welding_outbound"</li>
                        </ul>
                    </li>
                    <li><strong>测试其他工序步骤</strong>
                        <ul>
                            <li>测试 "领取PCB裸板" 工序</li>
                            <li>测试 "测试员领取焊接半成品" 工序</li>
                            <li>验证这些工序仍使用 <code>printSmtOutboundQrCode</code> 方法</li>
                            <li>验证 <code>label_type</code> 为 "smt_outbound"</li>
                        </ul>
                    </li>
                    <li><strong>参数传递测试</strong>
                        <ul>
                            <li>验证所有参数正确传递到 createQRCode.vue 页面</li>
                            <li>检查工序步骤名称是否正确显示</li>
                            <li>验证板型选择逻辑是否正常工作</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>📝 调试信息</h2>
            <div class="feature">
                <strong class="info">控制台日志</strong>
                <p>在 generateQRCode 方法中添加了详细的调试信息：</p>
                <div class="code-block">
console.log('生成二维码参数:', params)
console.log('工序步骤:', this.stepName)
console.log('使用的API方法:', apiMethod.name)
                </div>
                <p>通过这些日志可以验证：</p>
                <ul>
                    <li>工序步骤是否正确传递</li>
                    <li>选择的 API 方法是否正确</li>
                    <li>label_type 是否设置正确</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>⚠️ 注意事项</h2>
            <div class="feature">
                <strong class="warning">重要提醒</strong>
                <ul>
                    <li><strong>API 响应码</strong>：已修正为使用 <code>response.code === 0</code> 判断成功（WMS API 规范）</li>
                    <li><strong>参数编码</strong>：工序步骤名称使用 <code>encodeURIComponent</code> 进行 URL 编码</li>
                    <li><strong>向后兼容</strong>：现有的工序步骤功能保持不变，只有指定工序使用新的 API 方法</li>
                    <li><strong>错误处理</strong>：保持原有的错误处理逻辑，确保用户体验一致</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>✨ 实现亮点</h2>
            <ul>
                <li><strong>灵活的条件判断</strong>：通过工序步骤名称动态选择 API 方法和参数</li>
                <li><strong>清晰的用户反馈</strong>：在界面上显示当前工序步骤，提升用户体验</li>
                <li><strong>完整的调试支持</strong>：添加详细的控制台日志，便于问题排查</li>
                <li><strong>向后兼容性</strong>：不影响现有功能，只针对特定工序进行优化</li>
                <li><strong>代码可维护性</strong>：条件判断逻辑清晰，易于后续扩展其他工序</li>
            </ul>
        </div>
    </div>
</body>
</html>
