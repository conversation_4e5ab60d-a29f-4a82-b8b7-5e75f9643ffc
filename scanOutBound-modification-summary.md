# scanOutBound.vue 已领取数量显示功能修改总结

## 修改内容

### 1. 页面显示修改
- **位置**: 第27-30行
- **修改前**: 硬编码显示 "上板：0 下板：0 单板：0"
- **修改后**: 使用计算属性 `{{ receivedQuantityText }}` 动态显示

### 2. 数据属性添加
- **位置**: data() 方法中
- **新增属性**:
  ```javascript
  // 已领取数量
  onBoardCount: 0,      // 上板数量
  downBoardCount: 0,    // 下板数量  
  oneBoardCount: 0      // 单板数量
  ```

### 3. 计算属性添加
- **新增**: `receivedQuantityText` 计算属性
- **功能**: 根据板型动态显示已领取数量
- **逻辑**:
  - 上下板: 显示 "上板：[数量] 下板：[数量]"
  - 单板: 显示 "单板：[数量]"
  - 其他: 显示所有类型数量

### 4. 数据加载方法
- **新增**: `loadReceivedQuantity()` 方法
- **功能**: 调用 `getStepTaskDetail` API 获取已领取数量
- **数据映射**:
  - `response.data.onBoard` → `this.onBoardCount`
  - `response.data.downBoard` → `this.downBoardCount`
  - `response.data.oneBoard` → `this.oneBoardCount`

### 5. 页面加载流程修改
- **位置**: onLoad 方法中的 $nextTick
- **修改**: 在显示板型选择对话框前先加载已领取数量
- **流程**: 页面加载 → 获取已领取数量 → 显示板型选择对话框

### 6. 出库成功后更新
- **位置**: performOutbound 方法中
- **修改**: 出库成功后重新加载已领取数量
- **目的**: 确保显示的数量是最新的

## 显示效果

### 上下板产品
```
已领取数量：上板：5 下板：3
```

### 单板产品  
```
已领取数量：单板：8
```

### 其他情况
```
已领取数量：上板：5 下板：3 单板：8
```

## API 数据结构

### getStepTaskDetail 响应格式
```javascript
{
  code: 0,
  data: {
    onBoard: 5,      // 上板已领取数量
    downBoard: 3,    // 下板已领取数量
    oneBoard: 8      // 单板已领取数量
  }
}
```

## 错误处理
- API 调用失败时保持默认值 0
- 不影响正常的扫码流程
- 控制台输出详细日志便于调试

## 兼容性
- 保持与现有代码的兼容性
- 不影响现有的扫码和出库功能
- 样式与现有页面保持一致
